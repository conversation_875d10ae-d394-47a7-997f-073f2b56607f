"""
Town of West Haven Quest Definitions

This module contains quest definitions for the Town of West Haven.
"""

from typing import Dict, Any, List


def get_west_haven_quests() -> List[Dict[str, Any]]:
    """
    Get all quest definitions for the Town of West Haven.

    Returns:
        List of quest definition dictionaries
    """
    return [
        {
            "id": "goblin_threat_west_haven",
            "name": "Goblin Threat",
            "description": "The Mayor has reported a goblin spotted in town. Deal with this threat.",
            "objectives": ["defeat_goblin"],
            "metadata": {
                "level": "town_west_haven",
                "reward_gold": 15,
                "reward_experience": 30,
                "npc_giver": "mayor_west_haven",
                "goblin_spawn_position": {"x": 20, "y": 15},
                "difficulty": "easy"
            }
        },
        {
            "id": "west_haven_spider_sacs",
            "name": "Spider Silk Tapestry",
            "description": "The Mayor needs spider sacs to create a silk tapestry for the town hall.",
            "objectives": ["acquire_leather_armor", "collect_spider_sacs"],
            "metadata": {
                "level": "town_west_haven",
                "reward_gold": 100,
                "reward_experience": 200,
                "npc_giver": "mayor_west_haven",
                "prerequisite_quest": "goblin_threat_west_haven",
                "required_spider_sacs": 5,
                "required_armor_pieces": ["leather_cap", "leather_armor", "leather_pants", "leather_boots"],
                "spider_location": "green_acres_forest",
                "difficulty": "medium"
            }
        }
    ]


# Individual quest definitions for easy access
GOBLIN_THREAT_WEST_HAVEN = {
    "id": "goblin_threat_west_haven",
    "name": "Goblin Threat",
    "description": "The Mayor has reported a goblin spotted in town. Deal with this threat.",
    "objectives": ["defeat_goblin"],
    "metadata": {
        "level": "town_west_haven",
        "reward_gold": 15,
        "reward_experience": 30,
        "npc_giver": "mayor_west_haven",
        "goblin_spawn_position": {"x": 20, "y": 15},
        "difficulty": "easy"
    }
}

WEST_HAVEN_SPIDER_SACS = {
    "id": "west_haven_spider_sacs",
    "name": "Spider Silk Tapestry",
    "description": "The Mayor needs spider sacs to create a silk tapestry for the town hall.",
    "objectives": ["acquire_leather_armor", "collect_spider_sacs"],
    "metadata": {
        "level": "town_west_haven",
        "reward_gold": 100,
        "reward_experience": 200,
        "npc_giver": "mayor_west_haven",
        "prerequisite_quest": "goblin_threat_west_haven",
        "required_spider_sacs": 5,
        "required_armor_pieces": ["leather_cap", "leather_armor", "leather_pants", "leather_boots"],
        "spider_location": "green_acres_forest",
        "difficulty": "medium"
    }
}
