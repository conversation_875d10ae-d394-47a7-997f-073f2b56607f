"""
Town of West Haven Event Handlers

This module contains level-specific event handlers and quest logic for West Haven.
"""

import time
from typing import Optional, Dict, Any, List
from src.game_core.events import GameEvent
from src.game_core.quest_manager import QuestState
from src.game_core import Position
from src.application.use_cases import CreateMonsterUseCase
from src.infrastructure.logging import get_logger


class TownWestHavenEventHandlers:
    """Event handlers for West Haven level-specific logic."""

    def __init__(self, event_bus, quest_manager):
        """
        Initialize West Haven event handlers.

        Args:
            event_bus: Event bus for game events
            quest_manager: Quest manager for quest state
        """
        self.event_bus = event_bus
        self.quest_manager = quest_manager
        self.logger = get_logger(__name__)

        # Note: Dialog state is now managed by DialogOverrideManager and DialogManager

        # Register quest definitions
        if self.quest_manager:
            from .quest_definitions import get_west_haven_quests
            for quest_def in get_west_haven_quests():
                self.quest_manager.register_quest(quest_def)

        # Register this as a quest dialog provider
        from src.game_core.dialog_override_manager import get_dialog_override_manager
        dialog_override_manager = get_dialog_override_manager()
        dialog_override_manager.register_quest_dialog_provider(self)

    def register_handlers(self) -> None:
        """Register all event handlers for this level."""
        self.event_bus.subscribe("entity_defeated", self.on_monster_defeated)
        self.event_bus.subscribe("npc_interaction", self.on_npc_interaction)
        self.event_bus.subscribe("quest_started", self.on_quest_started)
        self.event_bus.subscribe("quest_completed", self.on_quest_completed)
        self.event_bus.subscribe("quest_closed", self.on_quest_closed)
        self.event_bus.subscribe("objective_completed", self.on_objective_completed)

    def unregister_handlers(self) -> None:
        """Unregister all event handlers when leaving the level."""
        self.event_bus.unsubscribe("entity_defeated", self.on_monster_defeated)
        self.event_bus.unsubscribe("npc_interaction", self.on_npc_interaction)
        self.event_bus.unsubscribe("quest_started", self.on_quest_started)
        self.event_bus.unsubscribe("quest_completed", self.on_quest_completed)
        self.event_bus.unsubscribe("quest_closed", self.on_quest_closed)
        self.event_bus.unsubscribe("objective_completed", self.on_objective_completed)
    
    def on_level_loaded(self, event: GameEvent) -> None:
        """Handle level loaded event."""
        self.logger.info("West Haven level loaded")
    
    def on_monster_defeated(self, event: GameEvent) -> None:
        """Handle monster defeated events for quest completion."""
        entity_id = event.data.get("entity_id", "")

        # Check if this is the quest goblin
        if "quest_goblin_goblin_threat_west_haven" in entity_id:
            self._complete_goblin_quest()
    
    def on_npc_interaction(self, event: GameEvent) -> None:
        """Handle player interactions with NPCs."""
        npc_id = event.data.get("npc_id", "")
        npc_position = event.data.get("npc_position")

        if not self.quest_manager:
            return

        # Handle quest progression for the mayor
        if npc_id.startswith("mayor"):
            self._handle_mayor_quest_progression(npc_id)

    def _handle_mayor_quest_progression(self, npc_id: str) -> None:
        """Handle quest progression when talking to the Mayor."""
        # Handle goblin quest first
        goblin_quest_id = "goblin_threat_west_haven"
        goblin_quest_state = self.quest_manager.get_quest_state(goblin_quest_id)

        # Start goblin quest on first interaction
        if goblin_quest_state == QuestState.NOT_STARTED:
            # Use a flag to ensure we only start the quest once
            if not hasattr(self, '_goblin_quest_started'):
                self._goblin_quest_started = True

                success = self.quest_manager.start_quest(goblin_quest_id)
                if success:
                    self.logger.info("Mayor: Quest started - Goblin Threat")
                    # Don't invalidate dialog here - let the conversation finish naturally
                    # The quest dialog will take effect on the next interaction

                    # Spawn the quest goblin
                    self._spawn_quest_goblin(goblin_quest_id)

        # Handle spider sacs quest progression
        spider_quest_id = "west_haven_spider_sacs"
        spider_quest_state = self.quest_manager.get_quest_state(spider_quest_id)

        # Start spider quest if goblin quest is closed and spider quest not started
        if (goblin_quest_state == QuestState.CLOSED and
            spider_quest_state == QuestState.NOT_STARTED):

            if not hasattr(self, '_spider_quest_started'):
                self._spider_quest_started = True
                success = self.quest_manager.start_quest(spider_quest_id)
                if success:
                    self.logger.info("Mayor: Quest started - Spider Silk Tapestry")

                    # Invalidate dialog cache to reflect quest state change
                    from src.game_core.dialog_override_manager import get_dialog_override_manager
                    get_dialog_override_manager().invalidate_quest_dialog()

        # Handle spider quest objective progression
        elif spider_quest_state == QuestState.ACTIVE:
            self._handle_spider_quest_objectives(spider_quest_id)

        # Handle goblin quest completion
        elif goblin_quest_state == QuestState.COMPLETED:
            # Close the quest and give rewards (only once)
            if not hasattr(self, '_goblin_quest_completed'):
                self._goblin_quest_completed = True

                success = self.quest_manager.close_quest(goblin_quest_id)
                if success:
                    self.logger.info("Mayor: Quest completed - Goblin Threat")

                    # Invalidate dialog cache to show completion dialog
                    from src.game_core.dialog_override_manager import get_dialog_override_manager
                    get_dialog_override_manager().invalidate_quest_dialog()

                    # Give reward to player
                    from src.game_core.events import GameEvent
                    import time
                    reward_event = GameEvent(
                        event_type="quest_reward",
                        timestamp=time.time(),
                        data={
                            "quest_id": goblin_quest_id,
                            "gold": 15,
                            "experience": 30
                        }
                    )
                    self.event_bus.publish(reward_event)

    def _handle_spider_quest_objectives(self, quest_id: str) -> None:
        """Handle spider quest objective progression."""
        # Check leather armor objective
        if not self.quest_manager.is_objective_completed(quest_id, "acquire_leather_armor"):
            if self._check_player_has_leather_armor_set():
                success = self.quest_manager.complete_objective(quest_id, "acquire_leather_armor")
                if success:
                    self.logger.info("Spider quest: Leather armor objective completed")
                    # Invalidate dialog cache to reflect quest state change
                    from src.game_core.dialog_override_manager import get_dialog_override_manager
                    get_dialog_override_manager().invalidate_quest_dialog()

        # Check spider sacs objective (only if leather armor is complete)
        elif not self.quest_manager.is_objective_completed(quest_id, "collect_spider_sacs"):
            if self._check_player_has_spider_sacs(5):
                success = self.quest_manager.complete_objective(quest_id, "collect_spider_sacs")
                if success:
                    self.logger.info("Spider quest: Spider sacs objective completed")
                    # Invalidate dialog cache to reflect quest state change
                    from src.game_core.dialog_override_manager import get_dialog_override_manager
                    get_dialog_override_manager().invalidate_quest_dialog()

        # Handle quest completion and rewards
        elif self.quest_manager.get_quest_state(quest_id) == QuestState.COMPLETED:
            if not hasattr(self, '_spider_quest_completed'):
                self._spider_quest_completed = True

                # Remove spider sacs from inventory
                self._remove_spider_sacs_from_inventory(5)

                success = self.quest_manager.close_quest(quest_id)
                if success:
                    self.logger.info("Mayor: Spider quest completed - Spider Silk Tapestry")

                    # Give rewards
                    from src.game_core.events import GameEvent
                    import time
                    reward_event = GameEvent(
                        event_type="quest_reward",
                        timestamp=time.time(),
                        data={
                            "quest_id": quest_id,
                            "gold": 100,
                            "experience": 200
                        }
                    )
                    self.event_bus.publish(reward_event)

                    # Invalidate dialog cache to reflect quest state change
                    from src.game_core.dialog_override_manager import get_dialog_override_manager
                    get_dialog_override_manager().invalidate_quest_dialog()

    def _remove_spider_sacs_from_inventory(self, count: int) -> None:
        """Remove spider sacs from player inventory."""
        try:
            from src.presentation.game_engine import GameEngine
            game_engine = GameEngine.get_instance()
            if not game_engine or not game_engine.game_state or not game_engine.game_state.player:
                return

            player = game_engine.game_state.player
            current_count = player.inventory.get("spider_sac", 0)
            if current_count >= count:
                player.inventory["spider_sac"] = current_count - count
                if player.inventory["spider_sac"] <= 0:
                    del player.inventory["spider_sac"]
                self.logger.info(f"Removed {count} spider sacs from player inventory")
        except Exception as e:
            self.logger.error(f"Error removing spider sacs from inventory: {e}")

    def on_quest_started(self, event) -> None:
        """Handle quest started events."""
        quest_id = event.data.get("quest_id")
        if quest_id == "goblin_threat_west_haven":
            self._spawn_quest_goblin(quest_id)
            self.logger.info(f"Quest started: {quest_id} - Goblin spawned!")

    def on_quest_completed(self, event) -> None:
        """Handle quest completed events."""
        quest_id = event.data.get("quest_id")
        self.logger.info(f"Quest completed: {quest_id}")

    def on_quest_closed(self, event) -> None:
        """Handle quest closed events."""
        quest_id = event.data.get("quest_id")
        self.logger.info(f"Quest closed: {quest_id} (rewards distributed)")

    def on_objective_completed(self, event) -> None:
        """Handle objective completed events."""
        quest_id = event.data.get("quest_id")
        objective_id = event.data.get("objective_id")
        self.logger.info(f"Objective completed: {objective_id} for quest {quest_id}")
    
    def _spawn_quest_goblin(self, quest_id: str) -> None:
        """Spawn the quest goblin at the designated position."""
        try:
            # Create goblin at position (20, 15) as specified in quest definition
            goblin_position = Position.from_tile_coords(20, 15, 128)  # 128 is tile size
            
            # Use CreateMonsterUseCase to properly initialize AI behaviors
            create_monster_use_case = CreateMonsterUseCase(self.event_bus)
            
            goblin = create_monster_use_case.execute(
                monster_id="goblin_grunt",
                position=goblin_position,
                name="Dangerous Goblin",
                custom_stats={
                    "hp": 35,
                    "max_hp": 35,
                    "strength": 9,
                    "defense": 3,
                    "speed": 6
                },
                custom_id=f"quest_goblin_{quest_id}_{int(time.time())}"
            )

            # Override the ID to match quest system expectations
            goblin.id = f"quest_goblin_{quest_id}_{int(time.time())}"

            # Emit event to spawn the goblin in the game world
            spawn_event = GameEvent(
                event_type="spawn_monster",
                timestamp=time.time(),
                data={
                    "monster": goblin,
                    "quest_id": quest_id
                }
            )
            self.event_bus.publish(spawn_event)

            self.logger.info(f"Spawned quest goblin {goblin.id} at position ({20}, {15}) with AI behaviors initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to spawn quest goblin: {e}")
    
    def _complete_goblin_quest(self) -> None:
        """Mark the goblin quest objective as completed."""
        quest_id = "goblin_threat_west_haven"

        if self.quest_manager.get_quest_state(quest_id) == QuestState.ACTIVE:
            # Complete the defeat_goblin objective
            success = self.quest_manager.complete_objective(quest_id, "defeat_goblin")
            if success:
                self.logger.info("Goblin quest objective completed - return to Mayor for reward")

                # Invalidate dialog cache to reflect quest state change
                from src.game_core.dialog_override_manager import get_dialog_override_manager
                get_dialog_override_manager().invalidate_quest_dialog()

    def _check_player_has_leather_armor_set(self) -> bool:
        """Check if player has full leather armor set equipped."""
        try:
            from src.presentation.game_engine import GameEngine
            game_engine = GameEngine.get_instance()
            if not game_engine or not game_engine.game_state or not game_engine.game_state.player:
                return False

            player = game_engine.game_state.player
            required_armor = ["leather_cap", "leather_armor", "leather_pants", "leather_boots"]
            equipped_items = [
                player.head_equipment,
                player.chest_equipment,
                player.legs_equipment,
                player.boots_equipment
            ]

            # Check if all required armor pieces are equipped
            for armor_piece in required_armor:
                if armor_piece not in equipped_items:
                    return False

            return True
        except Exception as e:
            self.logger.error(f"Error checking leather armor set: {e}")
            return False

    def _check_player_has_spider_sacs(self, required_count: int = 5) -> bool:
        """Check if player has enough spider sacs in inventory."""
        try:
            from src.presentation.game_engine import GameEngine
            game_engine = GameEngine.get_instance()
            if not game_engine or not game_engine.game_state or not game_engine.game_state.player:
                return False

            player = game_engine.game_state.player
            spider_sac_count = player.inventory.get("spider_sac", 0)
            return spider_sac_count >= required_count
        except Exception as e:
            self.logger.error(f"Error checking spider sacs: {e}")
            return False
    
    def get_npc_dialog(self, npc_id: str, npc_position: tuple) -> Optional[List[str]]:
        """Get custom dialog for NPCs based on quest state."""
        if not self.quest_manager:
            return None

        # Handle Mayor dialog based on quest state
        if npc_id.startswith("mayor"):
            return self._get_mayor_dialog_for_quest_state()

        return None

    def _get_mayor_dialog_for_quest_state(self) -> Optional[List[str]]:
        """Get Mayor dialog based on current quest state."""
        goblin_quest_id = "goblin_threat_west_haven"
        goblin_quest_state = self.quest_manager.get_quest_state(goblin_quest_id)

        spider_quest_id = "west_haven_spider_sacs"
        spider_quest_state = self.quest_manager.get_quest_state(spider_quest_id)

        # Handle spider quest dialog first (higher priority)
        if spider_quest_state != QuestState.NOT_STARTED:
            return self._get_spider_quest_dialog(spider_quest_id, spider_quest_state)

        # Handle goblin quest dialog
        if goblin_quest_state == QuestState.NOT_STARTED:
            # Return the full quest introduction dialog as a conversation
            return [
                "Welcome to West Haven, traveler! I am Mayor Aldwin, and I'm glad you've come to our peaceful town.",
                "We have everything a weary adventurer might need - an inn for rest, shops for supplies, and friendly folk.",
                "However, I must tell you about a troubling matter that has come to my attention recently...",
                "One of our citizens spotted a goblin lurking near the town outskirts. This is most unusual and concerning!",
                "Would you be willing to help us deal with this threat? I can offer you 15 gold pieces as a reward."
            ]

        elif goblin_quest_state == QuestState.ACTIVE:
            return ["The goblin is still out there somewhere near the town outskirts. Please be careful, and come back when you've dealt with it."]

        elif goblin_quest_state == QuestState.COMPLETED:
            return ["You've done it! You defeated the goblin! Here's your reward of 15 gold pieces. West Haven is safe once again."]

        elif goblin_quest_state == QuestState.CLOSED:
            # Check if we should start spider quest
            if spider_quest_state == QuestState.NOT_STARTED:
                return [
                    "Thank you again for keeping our town safe! You've proven yourself as a capable adventurer.",
                    "Actually, I have another matter that could use your expertise...",
                    "I'm planning to commission a beautiful silk tapestry for our town hall, but I need spider sacs to make it.",
                    "However, this task requires better protection than what you currently have.",
                    "Come back to me once you've acquired a full set of leather armor - cap, chest piece, pants, and boots.",
                    "The armourer in town should have everything you need."
                ]
            else:
                return ["Thank you again for keeping our town safe! You're always welcome in West Haven."]

        # Fallback - should not happen
        return None

    def _get_spider_quest_dialog(self, quest_id: str, quest_state: QuestState) -> Optional[List[str]]:
        """Get dialog for the spider sacs quest based on current state."""
        if quest_state == QuestState.ACTIVE:
            # Check which objectives are completed
            leather_armor_complete = self.quest_manager.is_objective_completed(quest_id, "acquire_leather_armor")
            spider_sacs_complete = self.quest_manager.is_objective_completed(quest_id, "collect_spider_sacs")

            if not leather_armor_complete:
                # Player needs to get leather armor first
                if self._check_player_has_leather_armor_set():
                    return [
                        "Excellent! I see you've acquired the full leather armor set.",
                        "Now you're properly protected for the dangerous task ahead.",
                        "I need you to travel to the forest in the southwest corner of Green Acres.",
                        "Hunt the spiders there and collect 5 spider sacs for me.",
                        "Be careful - forest spiders are aggressive and venomous!"
                    ]
                else:
                    return [
                        "I see you're working on getting the leather armor.",
                        "Remember, I need you to have the complete set: leather cap, chest piece, pants, and boots.",
                        "The armourer in town should have everything you need.",
                        "Come back when you're fully equipped."
                    ]

            elif not spider_sacs_complete:
                # Player has armor, needs spider sacs
                if self._check_player_has_spider_sacs(5):
                    return [
                        "Perfect! You've collected the spider sacs I need.",
                        "These will make a magnificent silk tapestry for our town hall.",
                        "Here's your reward: 100 gold pieces and my gratitude!",
                        "You've done West Haven a great service."
                    ]
                else:
                    spider_count = 0
                    try:
                        from src.presentation.game_engine import GameEngine
                        game_engine = GameEngine.get_instance()
                        if game_engine and game_engine.game_state and game_engine.game_state.player:
                            spider_count = game_engine.game_state.player.inventory.get("spider_sac", 0)
                    except:
                        pass

                    return [
                        f"You currently have {spider_count} spider sacs. I need 5 total.",
                        "Keep hunting the spiders in the Green Acres forest.",
                        "The southwest corner is where you'll find them.",
                        "Be careful out there!"
                    ]

        elif quest_state == QuestState.COMPLETED:
            return [
                "Perfect! You've collected the spider sacs I need.",
                "These will make a magnificent silk tapestry for our town hall.",
                "Here's your reward: 100 gold pieces and my gratitude!",
                "You've done West Haven a great service."
            ]

        elif quest_state == QuestState.CLOSED:
            return [
                "The silk tapestry is coming along beautifully thanks to your spider sacs!",
                "You're a true hero of West Haven. Thank you for all your help."
            ]

        return None
